(()=>{var e={};e.id=492,e.ids=[492],e.modules={446:e=>{e.exports={}},770:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2314:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},2380:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},2862:e=>{e.exports={}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3207:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4682:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=s(5239),t=s(8088),i=s(8170),o=s.n(i),a=s(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5106)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],h={require:s,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5106:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b,metadata:()=>u});var n=s(7413),t=s(2376),i=s.n(t),o=s(8726),a=s.n(o);s(1135),s(1120);var l=s(4536),d=s.n(l),c=s(2862),h=s.n(c);let m=({title:e="Cast Stone"})=>(0,n.jsx)("header",{className:h().header,children:(0,n.jsxs)("div",{className:h().container,children:[(0,n.jsx)("div",{className:h().logo,children:(0,n.jsx)(d(),{href:"/",className:h().logoLink,children:e})}),(0,n.jsx)("nav",{className:h().nav,children:(0,n.jsxs)("ul",{className:h().navList,children:[(0,n.jsx)("li",{children:(0,n.jsx)(d(),{href:"/",className:h().navLink,children:"Home"})}),(0,n.jsx)("li",{children:(0,n.jsx)(d(),{href:"/about",className:h().navLink,children:"About"})}),(0,n.jsx)("li",{children:(0,n.jsx)(d(),{href:"/users",className:h().navLink,children:"Users"})}),(0,n.jsx)("li",{children:(0,n.jsx)(d(),{href:"/contact",className:h().navLink,children:"Contact"})})]})})]})});var v=s(446),x=s.n(v);let p=({companyName:e="Cast Stone",year:r=new Date().getFullYear()})=>(0,n.jsx)("footer",{className:x().footer,children:(0,n.jsx)("div",{className:x().container,children:(0,n.jsxs)("div",{className:x().content,children:[(0,n.jsxs)("p",{className:x().copyright,children:["\xa9 ",r," ",e,". All rights reserved."]}),(0,n.jsxs)("div",{className:x().links,children:[(0,n.jsx)("a",{href:"/privacy",className:x().link,children:"Privacy Policy"}),(0,n.jsx)("a",{href:"/terms",className:x().link,children:"Terms of Service"})]})]})})}),u={title:"Cast Stone - Modern Web Application",description:"A modern web application built with Next.js and .NET Core"};function b({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsxs)("body",{className:`${i().variable} ${a().variable} antialiased`,children:[(0,n.jsx)(m,{}),(0,n.jsx)("main",{className:"min-h-screen",children:e}),(0,n.jsx)(p,{})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),n=r.X(0,[447,295],()=>s(4682));module.exports=n})();