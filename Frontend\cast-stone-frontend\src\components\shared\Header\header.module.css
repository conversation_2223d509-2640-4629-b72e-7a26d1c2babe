.header {
  @apply bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50;
}

.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  @apply flex items-center justify-between h-16;
}

.logo {
  @apply flex-shrink-0;
}

.logoLink {
  @apply text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200;
}

.nav {
  @apply hidden md:block;
}

.navList {
  @apply flex space-x-8;
}

.navLink {
  @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200;
}
