'use client';

import React, { useState, useEffect } from 'react';
import { userService } from '@/services/api';
import { User } from '@/types';
import styles from './usersList.module.css';

const UsersList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const result = await userService.getAll();

      if (result.success && result.data) {
        setUsers(result.data);
      } else {
        setError(result.message || 'Failed to load users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading users...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <p>Error: {error}</p>
          <button onClick={fetchUsers} className={styles.retryButton}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Users</h1>
        <button className={styles.addButton}>Add User</button>
      </div>
      
      {users.length === 0 ? (
        <div className={styles.emptyState}>
          <p>No users found.</p>
        </div>
      ) : (
        <div className={styles.userGrid}>
          {users.map((user) => (
            <div key={user.id} className={styles.userCard}>
              <div className={styles.userInfo}>
                <h3 className={styles.userName}>
                  {user.firstName} {user.lastName}
                </h3>
                <p className={styles.userEmail}>{user.email}</p>
                {user.phoneNumber && (
                  <p className={styles.userPhone}>{user.phoneNumber}</p>
                )}
                <div className={styles.userMeta}>
                  <span className={`${styles.status} ${user.isActive ? styles.active : styles.inactive}`}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <span className={styles.date}>
                    Joined: {new Date(user.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className={styles.userActions}>
                <button className={styles.editButton}>Edit</button>
                <button className={styles.deleteButton}>Delete</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UsersList;
