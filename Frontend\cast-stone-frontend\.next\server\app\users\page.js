(()=>{var e={};e.id=9,e.ids=[9],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},446:e=>{e.exports={}},770:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},930:(e,s,t)=>{Promise.resolve().then(t.bind(t,7976))},1135:()=>{},2314:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},2380:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},2862:e=>{e.exports={}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3207:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4103:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(7413),n=t(7976);function a(){return(0,r.jsx)(n.default,{})}},5106:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f,metadata:()=>x});var r=t(7413),n=t(2376),a=t.n(n),i=t(8726),o=t.n(i);t(1135),t(1120);var l=t(4536),c=t.n(l),d=t(2862),h=t.n(d);let u=({title:e="Cast Stone"})=>(0,r.jsx)("header",{className:h().header,children:(0,r.jsxs)("div",{className:h().container,children:[(0,r.jsx)("div",{className:h().logo,children:(0,r.jsx)(c(),{href:"/",className:h().logoLink,children:e})}),(0,r.jsx)("nav",{className:h().nav,children:(0,r.jsxs)("ul",{className:h().navList,children:[(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/",className:h().navLink,children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/about",className:h().navLink,children:"About"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/users",className:h().navLink,children:"Users"})}),(0,r.jsx)("li",{children:(0,r.jsx)(c(),{href:"/contact",className:h().navLink,children:"Contact"})})]})})]})});var m=t(446),p=t.n(m);let v=({companyName:e="Cast Stone",year:s=new Date().getFullYear()})=>(0,r.jsx)("footer",{className:p().footer,children:(0,r.jsx)("div",{className:p().container,children:(0,r.jsxs)("div",{className:p().content,children:[(0,r.jsxs)("p",{className:p().copyright,children:["\xa9 ",s," ",e,". All rights reserved."]}),(0,r.jsxs)("div",{className:p().links,children:[(0,r.jsx)("a",{href:"/privacy",className:p().link,children:"Privacy Policy"}),(0,r.jsx)("a",{href:"/terms",className:p().link,children:"Terms of Service"})]})]})})}),x={title:"Cast Stone - Modern Web Application",description:"A modern web application built with Next.js and .NET Core"};function f({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${a().variable} ${o().variable} antialiased`,children:[(0,r.jsx)(u,{}),(0,r.jsx)("main",{className:"min-h-screen",children:e}),(0,r.jsx)(v,{})]})})}},7976:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\components\\\\Users\\\\UsersList\\\\UsersList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Users\\UsersList\\UsersList.tsx","default")},7983:e=>{e.exports={}},9082:(e,s,t)=>{Promise.resolve().then(t.bind(t,9533))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9500:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=t(5239),n=t(8088),a=t(8170),i=t.n(a),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4103)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\users\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,5106)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\users\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9533:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var r=t(687),n=t(3210);class a{constructor(e){this.baseUrl=e}async request(e,s={}){let t=`${this.baseUrl}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s};try{let e=await fetch(t,r);if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async getUsers(){return this.request("/users")}async getUserById(e){return this.request(`/users/${e}`)}async getUserByEmail(e){return this.request(`/users/email/${e}`)}async getActiveUsers(){return this.request("/users/active")}async createUser(e){return this.request("/users",{method:"POST",body:JSON.stringify(e)})}async updateUser(e,s){return this.request(`/users/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteUser(e){return this.request(`/users/${e}`,{method:"DELETE"})}}let i=new a("https://localhost:7000/api"),o={getAll:()=>i.getUsers()};var l=t(7983),c=t.n(l);let d=()=>{let[e,s]=(0,n.useState)([]),[t,a]=(0,n.useState)(!0),[i,l]=(0,n.useState)(null);(0,n.useEffect)(()=>{d()},[]);let d=async()=>{try{a(!0);let e=await o.getAll();e.success&&e.data?s(e.data):l(e.message||"Failed to load users")}catch(e){l(e instanceof Error?e.message:"An error occurred")}finally{a(!1)}};return t?(0,r.jsx)("div",{className:c().container,children:(0,r.jsx)("div",{className:c().loading,children:"Loading users..."})}):i?(0,r.jsx)("div",{className:c().container,children:(0,r.jsxs)("div",{className:c().error,children:[(0,r.jsxs)("p",{children:["Error: ",i]}),(0,r.jsx)("button",{onClick:d,className:c().retryButton,children:"Retry"})]})}):(0,r.jsxs)("div",{className:c().container,children:[(0,r.jsxs)("div",{className:c().header,children:[(0,r.jsx)("h1",{className:c().title,children:"Users"}),(0,r.jsx)("button",{className:c().addButton,children:"Add User"})]}),0===e.length?(0,r.jsx)("div",{className:c().emptyState,children:(0,r.jsx)("p",{children:"No users found."})}):(0,r.jsx)("div",{className:c().userGrid,children:e.map(e=>(0,r.jsxs)("div",{className:c().userCard,children:[(0,r.jsxs)("div",{className:c().userInfo,children:[(0,r.jsxs)("h3",{className:c().userName,children:[e.firstName," ",e.lastName]}),(0,r.jsx)("p",{className:c().userEmail,children:e.email}),e.phoneNumber&&(0,r.jsx)("p",{className:c().userPhone,children:e.phoneNumber}),(0,r.jsxs)("div",{className:c().userMeta,children:[(0,r.jsx)("span",{className:`${c().status} ${e.isActive?c().active:c().inactive}`,children:e.isActive?"Active":"Inactive"}),(0,r.jsxs)("span",{className:c().date,children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:c().userActions,children:[(0,r.jsx)("button",{className:c().editButton,children:"Edit"}),(0,r.jsx)("button",{className:c().deleteButton,children:"Delete"})]})]},e.id))})]})}},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,295,658],()=>t(9500));module.exports=r})();