.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
}

.header {
  @apply flex justify-between items-center mb-8;
}

.title {
  @apply text-3xl font-bold text-gray-900;
}

.addButton {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
}

.loading {
  @apply text-center py-12 text-gray-600;
}

.error {
  @apply text-center py-12;
}

.retryButton {
  @apply mt-4 bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
}

.emptyState {
  @apply text-center py-12 text-gray-600;
}

.userGrid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.userCard {
  @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
}

.userInfo {
  @apply mb-4;
}

.userName {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.userEmail {
  @apply text-gray-600 mb-1;
}

.userPhone {
  @apply text-gray-600 mb-3;
}

.userMeta {
  @apply flex justify-between items-center;
}

.status {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.active {
  @apply bg-green-100 text-green-800;
}

.inactive {
  @apply bg-red-100 text-red-800;
}

.date {
  @apply text-xs text-gray-500;
}

.userActions {
  @apply flex space-x-2;
}

.editButton {
  @apply bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded text-sm transition-colors duration-200;
}

.deleteButton {
  @apply bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded text-sm transition-colors duration-200;
}
