(()=>{var e={};e.id=977,e.ids=[977],e.modules={440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},446:e=>{e.exports={}},770:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2314:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},2380:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2862:e=>{e.exports={}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3207:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3839:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(7413);function n(){return(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Contact Us"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Get in touch with our team"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Send us a message"}),(0,t.jsxs)("form",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject"}),(0,t.jsx)("input",{type:"text",id:"subject",name:"subject",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,t.jsx)("textarea",{id:"message",name:"message",rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsx)("button",{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200",children:"Send Message"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Contact Information"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Address"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["123 Technology Street",(0,t.jsx)("br",{}),"Innovation City, IC 12345",(0,t.jsx)("br",{}),"United States"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Phone"}),(0,t.jsx)("p",{className:"text-gray-600",children:"+****************"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Email"}),(0,t.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Business Hours"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Monday - Friday: 9:00 AM - 6:00 PM",(0,t.jsx)("br",{}),"Saturday: 10:00 AM - 4:00 PM",(0,t.jsx)("br",{}),"Sunday: Closed"]})]})]})]})]})]})}r(1120)},3873:e=>{"use strict";e.exports=require("path")},5106:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>v,metadata:()=>b});var t=r(7413),n=r(2376),a=r.n(n),i=r(8726),o=r.n(i);r(1135),r(1120);var l=r(4536),d=r.n(l),c=r(2862),m=r.n(c);let x=({title:e="Cast Stone"})=>(0,t.jsx)("header",{className:m().header,children:(0,t.jsxs)("div",{className:m().container,children:[(0,t.jsx)("div",{className:m().logo,children:(0,t.jsx)(d(),{href:"/",className:m().logoLink,children:e})}),(0,t.jsx)("nav",{className:m().nav,children:(0,t.jsxs)("ul",{className:m().navList,children:[(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/",className:m().navLink,children:"Home"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/about",className:m().navLink,children:"About"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/users",className:m().navLink,children:"Users"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/contact",className:m().navLink,children:"Contact"})})]})})]})});var h=r(446),u=r.n(h);let p=({companyName:e="Cast Stone",year:s=new Date().getFullYear()})=>(0,t.jsx)("footer",{className:u().footer,children:(0,t.jsx)("div",{className:u().container,children:(0,t.jsxs)("div",{className:u().content,children:[(0,t.jsxs)("p",{className:u().copyright,children:["\xa9 ",s," ",e,". All rights reserved."]}),(0,t.jsxs)("div",{className:u().links,children:[(0,t.jsx)("a",{href:"/privacy",className:u().link,children:"Privacy Policy"}),(0,t.jsx)("a",{href:"/terms",className:u().link,children:"Terms of Service"})]})]})})}),b={title:"Cast Stone - Modern Web Application",description:"A modern web application built with Next.js and .NET Core"};function v({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsxs)("body",{className:`${a().variable} ${o().variable} antialiased`,children:[(0,t.jsx)(x,{}),(0,t.jsx)("main",{className:"min-h-screen",children:e}),(0,t.jsx)(p,{})]})})}},6276:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3839)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,5106)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,295,658],()=>r(6276));module.exports=t})();