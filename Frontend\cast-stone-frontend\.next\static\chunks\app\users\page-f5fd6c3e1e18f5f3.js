(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{1091:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(5155),a=t(2115);class c{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseUrl).concat(e),r={headers:{"Content-Type":"application/json",...s.headers},...s};try{let e=await fetch(t,r);if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async getUsers(){return this.request("/users")}async getUserById(e){return this.request("/users/".concat(e))}async getUserByEmail(e){return this.request("/users/email/".concat(e))}async getActiveUsers(){return this.request("/users/active")}async createUser(e){return this.request("/users",{method:"POST",body:JSON.stringify(e)})}async updateUser(e,s){return this.request("/users/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteUser(e){return this.request("/users/".concat(e),{method:"DELETE"})}constructor(e){this.baseUrl=e}}let n=new c("https://localhost:7000/api"),i={getAll:()=>n.getUsers()};var l=t(4370),d=t.n(l);let o=()=>{let[e,s]=(0,a.useState)([]),[t,c]=(0,a.useState)(!0),[n,l]=(0,a.useState)(null);(0,a.useEffect)(()=>{o()},[]);let o=async()=>{try{c(!0);let e=await i.getAll();e.success&&e.data?s(e.data):l(e.message||"Failed to load users")}catch(e){l(e instanceof Error?e.message:"An error occurred")}finally{c(!1)}};return t?(0,r.jsx)("div",{className:d().container,children:(0,r.jsx)("div",{className:d().loading,children:"Loading users..."})}):n?(0,r.jsx)("div",{className:d().container,children:(0,r.jsxs)("div",{className:d().error,children:[(0,r.jsxs)("p",{children:["Error: ",n]}),(0,r.jsx)("button",{onClick:o,className:d().retryButton,children:"Retry"})]})}):(0,r.jsxs)("div",{className:d().container,children:[(0,r.jsxs)("div",{className:d().header,children:[(0,r.jsx)("h1",{className:d().title,children:"Users"}),(0,r.jsx)("button",{className:d().addButton,children:"Add User"})]}),0===e.length?(0,r.jsx)("div",{className:d().emptyState,children:(0,r.jsx)("p",{children:"No users found."})}):(0,r.jsx)("div",{className:d().userGrid,children:e.map(e=>(0,r.jsxs)("div",{className:d().userCard,children:[(0,r.jsxs)("div",{className:d().userInfo,children:[(0,r.jsxs)("h3",{className:d().userName,children:[e.firstName," ",e.lastName]}),(0,r.jsx)("p",{className:d().userEmail,children:e.email}),e.phoneNumber&&(0,r.jsx)("p",{className:d().userPhone,children:e.phoneNumber}),(0,r.jsxs)("div",{className:d().userMeta,children:[(0,r.jsx)("span",{className:"".concat(d().status," ").concat(e.isActive?d().active:d().inactive),children:e.isActive?"Active":"Inactive"}),(0,r.jsxs)("span",{className:d().date,children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:d().userActions,children:[(0,r.jsx)("button",{className:d().editButton,children:"Edit"}),(0,r.jsx)("button",{className:d().deleteButton,children:"Delete"})]})]},e.id))})]})}},3990:(e,s,t)=>{Promise.resolve().then(t.bind(t,1091))},4370:()=>{}},e=>{var s=s=>e(e.s=s);e.O(0,[753,441,684,358],()=>s(3990)),_N_E=e.O()}]);