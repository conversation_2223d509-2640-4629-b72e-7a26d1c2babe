using Cast_Stone_api.DTOs.Request;
using Cast_Stone_api.DTOs.Response;

namespace Cast_Stone_api.Services.Interfaces;

public interface IUserService
{
    Task<ApiResponse<UserResponse>> GetUserByIdAsync(int id);
    Task<ApiResponse<IEnumerable<UserResponse>>> GetAllUsersAsync();
    Task<ApiResponse<UserResponse>> CreateUserAsync(CreateUserRequest request);
    Task<ApiResponse<UserResponse>> UpdateUserAsync(int id, UpdateUserRequest request);
    Task<ApiResponse> DeleteUserAsync(int id);
    Task<ApiResponse<UserResponse>> GetUserByEmailAsync(string email);
    Task<ApiResponse<IEnumerable<UserResponse>>> GetActiveUsersAsync();
}
