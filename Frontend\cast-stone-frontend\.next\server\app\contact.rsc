1:"$Sreact.fragment"
2:I[6874,["177","static/chunks/app/layout-d361f2bbbf5c7df1.js"],""]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[9665,[],"OutletBoundary"]
8:I[4911,[],"AsyncMetadataOutlet"]
a:I[9665,[],"ViewportBoundary"]
c:I[9665,[],"MetadataBoundary"]
e:I[6614,[],""]
:HL["/_next/static/css/d9913778143b166b.css","style"]
0:{"P":null,"b":"0kQ-yJxVrr9O3Hqrk-zK7","p":"","c":["","contact"],"i":false,"f":[[["",{"children":["contact",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d9913778143b166b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":[["$","header",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":[["$","div",null,{"className":"$undefined","children":["$","$L2",null,{"href":"/","className":"$undefined","children":"Cast Stone"}]}],["$","nav",null,{"className":"$undefined","children":["$","ul",null,{"className":"$undefined","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/","className":"$undefined","children":"Home"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/about","className":"$undefined","children":"About"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/users","className":"$undefined","children":"Users"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/contact","className":"$undefined","children":"Contact"}]}]]}]}]]}]}],["$","main",null,{"className":"min-h-screen","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":[["$","p",null,{"className":"$undefined","children":["© ",2025," ","Cast Stone",". All rights reserved."]}],["$","div",null,{"className":"$undefined","children":[["$","a",null,{"href":"/privacy","className":"$undefined","children":"Privacy Policy"}],["$","a",null,{"href":"/terms","className":"$undefined","children":"Terms of Service"}]]}]]}]}]}]]}]}]]}],{"children":["contact",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h1",null,{"className":"text-4xl font-bold text-gray-900 mb-4","children":"Contact Us"}],["$","p",null,{"className":"text-xl text-gray-600","children":"Get in touch with our team"}]]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-12","children":[["$","div",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-900 mb-6","children":"Send us a message"}],["$","form",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","label",null,{"htmlFor":"name","className":"block text-sm font-medium text-gray-700 mb-2","children":"Name"}],["$","input",null,{"type":"text","id":"name","name":"name","className":"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500","required":true}]]}],["$","div",null,{"children":[["$","label",null,{"htmlFor":"email","className":"block text-sm font-medium text-gray-700 mb-2","children":"Email"}],["$","input",null,{"type":"email","id":"email","name":"email","className":"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500","required":true}]]}],["$","div",null,{"children":[["$","label",null,{"htmlFor":"subject","className":"block text-sm font-medium text-gray-700 mb-2","children":"Subject"}],["$","input",null,{"type":"text","id":"subject","name":"subject","className":"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500","required":true}]]}],["$","div",null,{"children":[["$","label",null,{"htmlFor":"message","className":"block text-sm font-medium text-gray-700 mb-2","children":"Message"}],["$","textarea",null,{"id":"message","name":"message","rows":6,"className":"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500","required":true}]]}],["$","button",null,{"type":"submit","className":"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200","children":"Send Message"}]]}]]}],["$","div",null,{"children":[["$","h2",null,{"className":"text-2xl font-semibold text-gray-900 mb-6","children":"Contact Information"}],["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-2","children":"Address"}],["$","p",null,{"className":"text-gray-600","children":["123 Technology Street",["$","br",null,{}],"Innovation City, IC 12345",["$","br",null,{}],"United States"]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-2","children":"Phone"}],["$","p",null,{"className":"text-gray-600","children":"+****************"}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-2","children":"Email"}],["$","p",null,{"className":"text-gray-600","children":"<EMAIL>"}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-2","children":"Business Hours"}],["$","p",null,{"className":"text-gray-600","children":["Monday - Friday: 9:00 AM - 6:00 PM",["$","br",null,{}],"Saturday: 10:00 AM - 4:00 PM",["$","br",null,{}],"Sunday: Closed"]}]]}]]}]]}]]}]]}],null,["$","$L5",null,{"children":["$L6","$L7",["$","$L8",null,{"promise":"$@9"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","KWYRlr-xd8gF2FeYkE7T8v",{"children":[["$","$La",null,{"children":"$Lb"}],null]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
f:"$Sreact.suspense"
10:I[4911,[],"AsyncMetadata"]
d:["$","div",null,{"hidden":true,"children":["$","$f",null,{"fallback":null,"children":["$","$L10",null,{"promise":"$@11"}]}]}]
7:null
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
6:null
9:{"metadata":[["$","title","0",{"children":"Cast Stone - Modern Web Application"}],["$","meta","1",{"name":"description","content":"A modern web application built with Next.js and .NET Core"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
11:{"metadata":"$9:metadata","error":null,"digest":"$undefined"}
