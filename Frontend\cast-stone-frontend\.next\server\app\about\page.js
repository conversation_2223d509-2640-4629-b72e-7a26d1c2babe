(()=>{var e={};e.id=220,e.ids=[220],e.modules={440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},446:e=>{e.exports={}},770:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2314:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},2380:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2664:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(5239),n=r(8088),i=r(8170),a=r.n(i),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8770)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,5106)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2862:e=>{e.exports={}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3207:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5106:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j,metadata:()=>v});var t=r(7413),n=r(2376),i=r.n(n),a=r(8726),l=r.n(a);r(1135),r(1120);var o=r(4536),d=r.n(o),c=r(2862),h=r.n(c);let x=({title:e="Cast Stone"})=>(0,t.jsx)("header",{className:h().header,children:(0,t.jsxs)("div",{className:h().container,children:[(0,t.jsx)("div",{className:h().logo,children:(0,t.jsx)(d(),{href:"/",className:h().logoLink,children:e})}),(0,t.jsx)("nav",{className:h().nav,children:(0,t.jsxs)("ul",{className:h().navList,children:[(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/",className:h().navLink,children:"Home"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/about",className:h().navLink,children:"About"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/users",className:h().navLink,children:"Users"})}),(0,t.jsx)("li",{children:(0,t.jsx)(d(),{href:"/contact",className:h().navLink,children:"Contact"})})]})})]})});var m=r(446),p=r.n(m);let u=({companyName:e="Cast Stone",year:s=new Date().getFullYear()})=>(0,t.jsx)("footer",{className:p().footer,children:(0,t.jsx)("div",{className:p().container,children:(0,t.jsxs)("div",{className:p().content,children:[(0,t.jsxs)("p",{className:p().copyright,children:["\xa9 ",s," ",e,". All rights reserved."]}),(0,t.jsxs)("div",{className:p().links,children:[(0,t.jsx)("a",{href:"/privacy",className:p().link,children:"Privacy Policy"}),(0,t.jsx)("a",{href:"/terms",className:p().link,children:"Terms of Service"})]})]})})}),v={title:"Cast Stone - Modern Web Application",description:"A modern web application built with Next.js and .NET Core"};function j({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsxs)("body",{className:`${i().variable} ${l().variable} antialiased`,children:[(0,t.jsx)(x,{}),(0,t.jsx)("main",{className:"min-h-screen",children:e}),(0,t.jsx)(u,{})]})})}},6487:()=>{},8335:()=>{},8770:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(7413);function n(){return(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"About Cast Stone"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Building the future with modern technology and innovative solutions"})]}),(0,t.jsxs)("div",{className:"prose prose-lg mx-auto",children:[(0,t.jsx)("h2",{children:"Our Mission"}),(0,t.jsx)("p",{children:"Cast Stone is dedicated to creating robust, scalable, and modern web applications that meet the evolving needs of businesses and users alike. We leverage cutting-edge technologies to deliver exceptional digital experiences."}),(0,t.jsx)("h2",{children:"Technology Stack"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 not-prose",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Frontend"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,t.jsx)("li",{children:"• Next.js 14 with App Router"}),(0,t.jsx)("li",{children:"• TypeScript"}),(0,t.jsx)("li",{children:"• Tailwind CSS"}),(0,t.jsx)("li",{children:"• Component-based Architecture"})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Backend"}),(0,t.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,t.jsx)("li",{children:"• .NET 8 Web API"}),(0,t.jsx)("li",{children:"• MongoDB with MongoDB.Driver"}),(0,t.jsx)("li",{children:"• Repository Pattern"}),(0,t.jsx)("li",{children:"• Clean Architecture"})]})]})]}),(0,t.jsx)("h2",{children:"Features"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)("li",{children:"Modern, responsive design"}),(0,t.jsx)("li",{children:"RESTful API architecture"}),(0,t.jsx)("li",{children:"Type-safe development"}),(0,t.jsx)("li",{children:"Scalable database design"}),(0,t.jsx)("li",{children:"Component-based UI"})]})]})]})}r(1120)},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,295,658],()=>r(2664));module.exports=t})();