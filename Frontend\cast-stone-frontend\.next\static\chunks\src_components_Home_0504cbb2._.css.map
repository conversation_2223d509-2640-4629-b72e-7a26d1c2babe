{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/heroSection.module.css"], "sourcesContent": ["\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/FeaturesSection/featuresSection.module.css"], "sourcesContent": ["\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/homeComponent.module.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n.homeComponent {\n  min-height: 100vh;\n}\n"], "names": [], "mappings": "AACA", "debugId": null}}]}