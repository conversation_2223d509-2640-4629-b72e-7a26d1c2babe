1:"$Sreact.fragment"
2:I[6874,["177","static/chunks/app/layout-d361f2bbbf5c7df1.js"],""]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[1091,["9","static/chunks/app/users/page-f5fd6c3e1e18f5f3.js"],"default"]
6:I[9665,[],"OutletBoundary"]
9:I[4911,[],"AsyncMetadataOutlet"]
b:I[9665,[],"ViewportBoundary"]
d:I[9665,[],"MetadataBoundary"]
f:I[6614,[],""]
:HL["/_next/static/css/d9913778143b166b.css","style"]
:HL["/_next/static/css/ef46db3751d8e999.css","style"]
0:{"P":null,"b":"0kQ-yJxVrr9O3Hqrk-zK7","p":"","c":["","users"],"i":false,"f":[[["",{"children":["users",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d9913778143b166b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":[["$","header",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":[["$","div",null,{"className":"$undefined","children":["$","$L2",null,{"href":"/","className":"$undefined","children":"Cast Stone"}]}],["$","nav",null,{"className":"$undefined","children":["$","ul",null,{"className":"$undefined","children":[["$","li",null,{"children":["$","$L2",null,{"href":"/","className":"$undefined","children":"Home"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/about","className":"$undefined","children":"About"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/users","className":"$undefined","children":"Users"}]}],["$","li",null,{"children":["$","$L2",null,{"href":"/contact","className":"$undefined","children":"Contact"}]}]]}]}]]}]}],["$","main",null,{"className":"min-h-screen","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":["$","div",null,{"className":"$undefined","children":[["$","p",null,{"className":"$undefined","children":["© ",2025," ","Cast Stone",". All rights reserved."]}],["$","div",null,{"className":"$undefined","children":[["$","a",null,{"href":"/privacy","className":"$undefined","children":"Privacy Policy"}],["$","a",null,{"href":"/terms","className":"$undefined","children":"Terms of Service"}]]}]]}]}]}]]}]}]]}],{"children":["users",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{}],[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/ef46db3751d8e999.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","$L6",null,{"children":["$L7","$L8",["$","$L9",null,{"promise":"$@a"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","xLKh0CnFpeTuILvizsbJtv",{"children":[["$","$Lb",null,{"children":"$Lc"}],null]}],["$","$Ld",null,{"children":"$Le"}]]}],false]],"m":"$undefined","G":["$f","$undefined"],"s":false,"S":true}
10:"$Sreact.suspense"
11:I[4911,[],"AsyncMetadata"]
e:["$","div",null,{"hidden":true,"children":["$","$10",null,{"fallback":null,"children":["$","$L11",null,{"promise":"$@12"}]}]}]
8:null
c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
7:null
a:{"metadata":[["$","title","0",{"children":"Cast Stone - Modern Web Application"}],["$","meta","1",{"name":"description","content":"A modern web application built with Next.js and .NET Core"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
12:{"metadata":"$a:metadata","error":null,"digest":"$undefined"}
