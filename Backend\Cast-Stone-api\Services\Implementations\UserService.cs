using Cast_Stone_api.Domain.Models;
using Cast_Stone_api.DTOs.Request;
using Cast_Stone_api.DTOs.Response;
using Cast_Stone_api.Repositories.Interfaces;
using Cast_Stone_api.Services.Interfaces;

namespace Cast_Stone_api.Services.Implementations;

public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;

    public UserService(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<ApiResponse<UserResponse>> GetUserByIdAsync(int id)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return ApiResponse<UserResponse>.ErrorResponse("User not found");
            }

            var userResponse = MapToUserResponse(user);
            return ApiResponse<UserResponse>.SuccessResponse(userResponse);
        }
        catch (Exception ex)
        {
            return ApiResponse<UserResponse>.ErrorResponse($"Error retrieving user: {ex.Message}");
        }
    }

    public async Task<ApiResponse<IEnumerable<UserResponse>>> GetAllUsersAsync()
    {
        try
        {
            var users = await _userRepository.GetAllAsync();
            var userResponses = users.Select(MapToUserResponse);
            return ApiResponse<IEnumerable<UserResponse>>.SuccessResponse(userResponses);
        }
        catch (Exception ex)
        {
            return ApiResponse<IEnumerable<UserResponse>>.ErrorResponse($"Error retrieving users: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserResponse>> CreateUserAsync(CreateUserRequest request)
    {
        try
        {
            // Check if email already exists
            if (await _userRepository.EmailExistsAsync(request.Email))
            {
                return ApiResponse<UserResponse>.ErrorResponse("Email already exists");
            }

            var user = new User
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber
            };

            var createdUser = await _userRepository.CreateAsync(user);
            var userResponse = MapToUserResponse(createdUser);
            return ApiResponse<UserResponse>.SuccessResponse(userResponse, "User created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<UserResponse>.ErrorResponse($"Error creating user: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserResponse>> UpdateUserAsync(int id, UpdateUserRequest request)
    {
        try
        {
            var existingUser = await _userRepository.GetByIdAsync(id);
            if (existingUser == null)
            {
                return ApiResponse<UserResponse>.ErrorResponse("User not found");
            }

            // Check if email is being updated and if it already exists
            if (!string.IsNullOrEmpty(request.Email) && request.Email != existingUser.Email)
            {
                if (await _userRepository.EmailExistsAsync(request.Email))
                {
                    return ApiResponse<UserResponse>.ErrorResponse("Email already exists");
                }
                existingUser.Email = request.Email;
            }

            // Update other fields if provided
            if (!string.IsNullOrEmpty(request.FirstName))
                existingUser.FirstName = request.FirstName;

            if (!string.IsNullOrEmpty(request.LastName))
                existingUser.LastName = request.LastName;

            if (request.PhoneNumber != null)
                existingUser.PhoneNumber = request.PhoneNumber;

            if (request.IsActive.HasValue)
                existingUser.IsActive = request.IsActive.Value;

            var updatedUser = await _userRepository.UpdateAsync(existingUser);
            var userResponse = MapToUserResponse(updatedUser);
            return ApiResponse<UserResponse>.SuccessResponse(userResponse, "User updated successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<UserResponse>.ErrorResponse($"Error updating user: {ex.Message}");
        }
    }

    public async Task<ApiResponse> DeleteUserAsync(int id)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return ApiResponse.ErrorResponse("User not found");
            }

            var deleted = await _userRepository.SoftDeleteAsync(id);
            if (deleted)
            {
                return ApiResponse.SuccessResponse("User deleted successfully");
            }

            return ApiResponse.ErrorResponse("Failed to delete user");
        }
        catch (Exception ex)
        {
            return ApiResponse.ErrorResponse($"Error deleting user: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserResponse>> GetUserByEmailAsync(string email)
    {
        try
        {
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null)
            {
                return ApiResponse<UserResponse>.ErrorResponse("User not found");
            }

            var userResponse = MapToUserResponse(user);
            return ApiResponse<UserResponse>.SuccessResponse(userResponse);
        }
        catch (Exception ex)
        {
            return ApiResponse<UserResponse>.ErrorResponse($"Error retrieving user: {ex.Message}");
        }
    }

    public async Task<ApiResponse<IEnumerable<UserResponse>>> GetActiveUsersAsync()
    {
        try
        {
            var users = await _userRepository.GetActiveUsersAsync();
            var userResponses = users.Select(MapToUserResponse);
            return ApiResponse<IEnumerable<UserResponse>>.SuccessResponse(userResponses);
        }
        catch (Exception ex)
        {
            return ApiResponse<IEnumerable<UserResponse>>.ErrorResponse($"Error retrieving active users: {ex.Message}");
        }
    }

    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse
        {
            Id = user.Id,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}
