{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\nimport styles from './heroSection.module.css';\n\ninterface HeroSectionProps {\n  title?: string;\n  subtitle?: string;\n  ctaText?: string;\n  onCtaClick?: () => void;\n}\n\nconst HeroSection: React.FC<HeroSectionProps> = ({\n  title = \"Welcome to Cast Stone\",\n  subtitle = \"Building the future with modern technology\",\n  ctaText = \"Get Started\",\n  onCtaClick\n}) => {\n  return (\n    <section className={styles.heroSection}>\n      <div className={styles.container}>\n        <div className={styles.content}>\n          <h1 className={styles.title}>{title}</h1>\n          <p className={styles.subtitle}>{subtitle}</p>\n          {onCtaClick && (\n            <button \n              className={styles.ctaButton}\n              onClick={onCtaClick}\n            >\n              {ctaText}\n            </button>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,uBAAuB,EAC/B,WAAW,4CAA4C,EACvD,UAAU,aAAa,EACvB,UAAU,EACX;IACC,qBACE,8OAAC;QAAQ,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;kBACpC,cAAA,8OAAC;YAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBAAG,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;kCAAG;;;;;;kCAC9B,8OAAC;wBAAE,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAG;;;;;;oBAC/B,4BACC,8OAAC;wBACC,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;wBAC3B,SAAS;kCAER;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/FeaturesSection/featuresSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/FeaturesSection/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport styles from './featuresSection.module.css';\n\ninterface Feature {\n  id: string;\n  title: string;\n  description: string;\n  icon?: string;\n}\n\ninterface FeaturesSectionProps {\n  title?: string;\n  features?: Feature[];\n}\n\nconst defaultFeatures: Feature[] = [\n  {\n    id: '1',\n    title: 'Modern Architecture',\n    description: 'Built with the latest technologies and best practices for scalability and performance.',\n    icon: '🏗️'\n  },\n  {\n    id: '2',\n    title: 'Responsive Design',\n    description: 'Fully responsive design that works seamlessly across all devices and screen sizes.',\n    icon: '📱'\n  },\n  {\n    id: '3',\n    title: 'Fast Performance',\n    description: 'Optimized for speed with efficient code and modern development practices.',\n    icon: '⚡'\n  }\n];\n\nconst FeaturesSection: React.FC<FeaturesSectionProps> = ({\n  title = \"Why Choose Cast Stone?\",\n  features = defaultFeatures\n}) => {\n  return (\n    <section className={styles.featuresSection}>\n      <div className={styles.container}>\n        <h2 className={styles.title}>{title}</h2>\n        <div className={styles.featuresGrid}>\n          {features.map((feature) => (\n            <div key={feature.id} className={styles.featureCard}>\n              {feature.icon && (\n                <div className={styles.icon}>{feature.icon}</div>\n              )}\n              <h3 className={styles.featureTitle}>{feature.title}</h3>\n              <p className={styles.featureDescription}>{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AACA;;;AAcA,MAAM,kBAA6B;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAED,MAAM,kBAAkD,CAAC,EACvD,QAAQ,wBAAwB,EAChC,WAAW,eAAe,EAC3B;IACC,qBACE,8OAAC;QAAQ,WAAW,2KAAA,CAAA,UAAM,CAAC,eAAe;kBACxC,cAAA,8OAAC;YAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAG,WAAW,2KAAA,CAAA,UAAM,CAAC,KAAK;8BAAG;;;;;;8BAC9B,8OAAC;oBAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,YAAY;8BAChC,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAAqB,WAAW,2KAAA,CAAA,UAAM,CAAC,WAAW;;gCAChD,QAAQ,IAAI,kBACX,8OAAC;oCAAI,WAAW,2KAAA,CAAA,UAAM,CAAC,IAAI;8CAAG,QAAQ,IAAI;;;;;;8CAE5C,8OAAC;oCAAG,WAAW,2KAAA,CAAA,UAAM,CAAC,YAAY;8CAAG,QAAQ,KAAK;;;;;;8CAClD,8OAAC;oCAAE,WAAW,2KAAA,CAAA,UAAM,CAAC,kBAAkB;8CAAG,QAAQ,WAAW;;;;;;;2BALrD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAYhC;uCAEe", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"homeComponent\": \"homeComponent-module__wtPNwW__homeComponent\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HomeComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport HeroSection from './HeroSection/HeroSection';\nimport FeaturesSection from './FeaturesSection/FeaturesSection';\nimport styles from './homeComponent.module.css';\n\nconst HomeComponent: React.FC = () => {\n  const handleGetStarted = () => {\n    // Navigate to getting started page or scroll to features\n    const featuresSection = document.getElementById('features');\n    if (featuresSection) {\n      featuresSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <div className={styles.homeComponent}>\n      <HeroSection \n        title=\"Welcome to Cast Stone\"\n        subtitle=\"Building the future with modern technology and innovative solutions\"\n        ctaText=\"Explore Features\"\n        onCtaClick={handleGetStarted}\n      />\n      <div id=\"features\">\n        <FeaturesSection />\n      </div>\n    </div>\n  );\n};\n\nexport default HomeComponent;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,gBAA0B;IAC9B,MAAM,mBAAmB;QACvB,yDAAyD;QACzD,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,sJAAA,CAAA,UAAM,CAAC,aAAa;;0BAClC,8OAAC,wJAAA,CAAA,UAAW;gBACV,OAAM;gBACN,UAAS;gBACT,SAAQ;gBACR,YAAY;;;;;;0BAEd,8OAAC;gBAAI,IAAG;0BACN,cAAA,8OAAC,gKAAA,CAAA,UAAe;;;;;;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}