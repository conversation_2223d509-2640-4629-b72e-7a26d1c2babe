[{"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\users\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\FeaturesSection\\FeaturesSection.tsx": "6", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HeroSection\\HeroSection.tsx": "7", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HomeComponent.tsx": "8", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Footer\\Footer.tsx": "9", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\Header.tsx": "10", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Users\\UsersList\\UsersList.tsx": "11", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api.ts": "12", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\types\\index.ts": "13"}, {"size": 2047, "mtime": 1751574271681, "results": "14", "hashOfConfig": "15"}, {"size": 4100, "mtime": 1751574288226, "results": "16", "hashOfConfig": "15"}, {"size": 946, "mtime": 1751574249569, "results": "17", "hashOfConfig": "15"}, {"size": 125, "mtime": 1751574229195, "results": "18", "hashOfConfig": "15"}, {"size": 129, "mtime": 1751574259666, "results": "19", "hashOfConfig": "15"}, {"size": 1630, "mtime": 1751574087008, "results": "20", "hashOfConfig": "15"}, {"size": 911, "mtime": 1751574067406, "results": "21", "hashOfConfig": "15"}, {"size": 911, "mtime": 1751574486977, "results": "22", "hashOfConfig": "15"}, {"size": 782, "mtime": 1751574134154, "results": "23", "hashOfConfig": "15"}, {"size": 1182, "mtime": 1751574118163, "results": "24", "hashOfConfig": "15"}, {"size": 2912, "mtime": 1751574456801, "results": "25", "hashOfConfig": "15"}, {"size": 3115, "mtime": 1751574310840, "results": "26", "hashOfConfig": "15"}, {"size": 1194, "mtime": 1751574322741, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1luihjl", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\FeaturesSection\\FeaturesSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HeroSection\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HomeComponent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Footer\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Users\\UsersList\\UsersList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\types\\index.ts", [], []]